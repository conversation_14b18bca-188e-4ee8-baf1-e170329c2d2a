import { TelegramClient } from "telegram";
import { StringSession } from "telegram/sessions";
import readline from "readline";

const apiId = 14431416;
const apiHash = "da8fa0a17dd9e0c1b9e420d73a39a710";

const stringSession = new StringSession("");

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

(async () => {
  const client = new TelegramClient(stringSession, apiId, apiHash, {
    connectionRetries: 5,
  });

  // Authenticate
  await client.start({
    phoneNumber: () =>
      new Promise((resolve) => rl.question("Enter phone number: ", resolve)),
    phoneCode: () =>
      new Promise((resolve) => rl.question("Enter code: ", resolve)),
    password: () =>
      new Promise((resolve) =>
        rl.question("Enter password (if 2FA): ", resolve)
      ),
    onError: (err) => console.log("Error:", err),
  });

  console.log("Connected. Session:", client.session.save());

  try {
    // Fetch Star Gifts
    const result = await client.invoke(
      new Api.payments.GetStarGifts({ hash: 0 })
    );

    // Filter unrevealed gifts (not sold out, not upgraded)
    const unrevealedGifts = result.gifts.filter(
      (gift) => !gift.sold_out && !gift.limited // Assuming unrevealed = not sold out and not limited
    );

    console.log("Unrevealed Gifts:");
    if (unrevealedGifts.length === 0) {
      console.log("No unrevealed gifts found.");
    } else {
      unrevealedGifts.forEach((gift) => {
        console.log(`- Gift ID: ${gift.id}, Stars: ${gift.stars}`);
        console.log(
          `  Sticker: ${gift.sticker.id}, Sold Out: ${
            gift.sold_out ? "Yes" : "No"
          }`
        );
      });
    }
  } catch (error) {
    console.error("Error fetching gifts:", error.message);
  }

  // Cleanup
  await client.disconnect();
  rl.close();
})();
