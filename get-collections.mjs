import { TelegramClient, Api } from "telegram";
import { StringSession } from "telegram/sessions/index.js";
import readline from "readline";

const apiId = 14431416;
const apiHash = "da8fa0a17dd9e0c1b9e420d73a39a710";

const stringSession = new StringSession("");

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

(async () => {
  const client = new TelegramClient(stringSession, apiId, apiHash, {
    connectionRetries: 5,
  });

  // Authenticate
  await client.start({
    phoneNumber: () =>
      new Promise((resolve) => rl.question("Enter phone number: ", resolve)),
    phoneCode: () =>
      new Promise((resolve) => rl.question("Enter code: ", resolve)),
    password: () =>
      new Promise((resolve) =>
        rl.question("Enter password (if 2FA): ", resolve)
      ),
    onError: (err) => console.log("Error:", err),
  });

  console.log("Connected. Session:", client.session.save());

  try {
    // Fetch Star Gifts
    const result = await client.invoke(
      new Api.payments.GetStarGifts({ hash: 0 })
    );

    console.log(`\n📊 Total Star Gifts Found: ${result.gifts.length}\n`);

    // Categorize gifts
    const availableGifts = result.gifts.filter(
      (gift) => !gift.soldOut && gift.availabilityRemains > 0
    );
    const soldOutGifts = result.gifts.filter((gift) => gift.soldOut);
    const limitedGifts = result.gifts.filter((gift) => gift.limited);
    const upgradableGifts = result.gifts.filter(
      (gift) => gift.upgradeStars && gift.upgradeStars.value > 0
    );

    console.log("🎁 AVAILABLE GIFTS (Not Sold Out & Stock Available):");
    console.log("=".repeat(60));

    if (availableGifts.length === 0) {
      console.log("❌ No available gifts found.");
    } else {
      availableGifts.forEach((gift, index) => {
        const giftId = gift.id.value || gift.id;
        const stars = gift.stars.value || gift.stars;
        const upgradeStars = gift.upgradeStars
          ? gift.upgradeStars.value || gift.upgradeStars
          : 0;
        const convertStars = gift.convertStars
          ? gift.convertStars.value || gift.convertStars
          : 0;

        console.log(`\n${index + 1}. 🌟 Gift ID: ${giftId}`);
        console.log(`   💰 Price: ${stars} stars`);
        console.log(
          `   📦 Stock: ${gift.availabilityRemains}/${gift.availabilityTotal}`
        );
        console.log(`   🏷️  Limited Edition: ${gift.limited ? "Yes" : "No"}`);
        console.log(`   🎂 Birthday Gift: ${gift.birthday ? "Yes" : "No"}`);

        if (upgradeStars > 0) {
          console.log(`   ⬆️  Upgrade Cost: ${upgradeStars} stars`);
        }

        if (convertStars > 0) {
          console.log(`   💱 Convert Value: ${convertStars} stars`);
        }

        if (gift.firstSaleDate) {
          const firstSale = new Date(gift.firstSaleDate * 1000);
          console.log(`   📅 First Sale: ${firstSale.toLocaleDateString()}`);
        }

        if (gift.lastSaleDate) {
          const lastSale = new Date(gift.lastSaleDate * 1000);
          console.log(`   📅 Last Sale: ${lastSale.toLocaleDateString()}`);
        }

        console.log(
          `   🎨 Sticker ID: ${gift.sticker.id.value || gift.sticker.id}`
        );
      });
    }

    // Summary statistics
    console.log("\n📈 SUMMARY STATISTICS:");
    console.log("=".repeat(40));
    console.log(`🟢 Available: ${availableGifts.length}`);
    console.log(`🔴 Sold Out: ${soldOutGifts.length}`);
    console.log(`⭐ Limited Edition: ${limitedGifts.length}`);
    console.log(`🔄 Upgradable: ${upgradableGifts.length}`);

    // Price distribution
    const priceDistribution = {};
    result.gifts.forEach((gift) => {
      const stars = gift.stars.value || gift.stars;
      priceDistribution[stars] = (priceDistribution[stars] || 0) + 1;
    });

    console.log("\n💰 PRICE DISTRIBUTION:");
    console.log("=".repeat(30));
    Object.entries(priceDistribution)
      .sort(([a], [b]) => parseInt(a) - parseInt(b))
      .forEach(([price, count]) => {
        console.log(`${price} stars: ${count} gifts`);
      });
  } catch (error) {
    console.error("❌ Error fetching gifts:", error.message);
    if (error.stack) {
      console.error("Stack trace:", error.stack);
    }
  }

  // Cleanup
  await client.disconnect();
  rl.close();
})();
